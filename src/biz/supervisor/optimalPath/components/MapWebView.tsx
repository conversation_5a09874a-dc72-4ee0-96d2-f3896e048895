import { useNavigation } from '@react-navigation/native';
import LoadingView from '@src/components/Loading';
import { WebViewPlugin } from '@src/components/WebView/WebViewPlugin';
import { handleBridgeMessage } from '@tastien/rn-bridge/lib/native';
import { Linking, Platform, Text, View } from 'react-native';
import Config from 'react-native-config';
import RNWebView from 'react-native-webview';

interface MapWebViewProps {
  url: string;
  onCallback?: (data: any) => void;
}

const nativePlugin = new WebViewPlugin();

export const MapWebView = ({ url, onCallback }: MapWebViewProps) => {
  const navigation = useNavigation<TstOm.StackParams.ScreenNavigationProp['navigation']>();

  const onMessage = (event: { nativeEvent: { data: string } }) => {
    const { data } = event.nativeEvent;

    if (!data) {
      return;
    }

    // 添加 rn 与 h5 的通信
    handleBridgeMessage(data, nativePlugin.getWebViewRef, navigation);

    try {
      const msg = typeof data === 'string' && JSON.parse(data);

      if (!msg) {
        return;
      }

      onCallback?.(msg);

      if (msg?.openURL) {
        Linking.openURL(msg.openURL);
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <RNWebView
      ref={nativePlugin.getWebViewRef}
      startInLoadingState={true}
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}
      sharedCookiesEnabled={true}
      webviewDebuggingEnabled={__DEV__}
      cacheEnabled={false}
      cacheMode="LOAD_NO_CACHE"
      contentMode="mobile"
      mixedContentMode="compatibility"
      androidLayerType="hardware"
      javaScriptEnabled={true}
      domStorageEnabled={true}
      allowsBackForwardNavigationGestures
      originWhitelist={Platform.OS === 'ios' ? undefined : ['tel:*', 'https:*', 'http:*']}
      onMessage={onMessage}
      source={{
        uri: url,
        headers: {
          tag: Config.APP_TAG_GRAY,
        },
      }}
      renderLoading={() => (
        <View className="size-full items-center justify-center">
          <LoadingView />
        </View>
      )}
      renderError={(_, errorCode: number, errorDesc: string) => (
        <View className="size-full items-center justify-center">
          <Text>{errorDesc}</Text>
          <Text>加载失败~</Text>
        </View>
      )}
    />
  );
};
