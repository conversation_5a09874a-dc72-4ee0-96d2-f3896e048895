import { FoodSafetyNormalRoutShopInfoDTO } from '@src/http/service/tactics/supervisorTask';
import Icon from '@src/ui/components/Icon';
import { cn } from '@src/utils';
import { Text, TouchableOpacity, View } from 'react-native';

export const TimeLine = ({
  shopInfoDTOS,
  handGoToway,
}: {
  shopInfoDTOS?: FoodSafetyNormalRoutShopInfoDTO[];
  handGoToway: ({ location, index }: { location: string[]; index: number }) => void;
}) => {
  return (
    <View className="flex-col gap-y-2">
      {shopInfoDTOS?.map((item, index) => {
        return (
          <View className="flex-row gap-x-3">
            <View className="flex-col items-center justify-center gap-y-2">
              <View
                className={cn('size-6 rounded-full bg-[#23C343] flex-col items-center justify-center', {
                  'bg-[#C9CDD4]': ['COMPLETED', 'AUDITING'].includes(item?.taskStatus),
                })}
              >
                <Text className="text-display-base font-medium text-white">{index + 1}</Text>
              </View>
              <View className="w-[1px] flex-1 bg-[#E5E6EB]" />
            </View>
            <View className="flex-1 flex-col">
              <View className="flex-row items-center justify-between">
                <View className="flex-row items-center gap-x-2">
                  {index === 0 && (
                    <View className="flex h-4 w-8 items-center justify-center rounded-[2px] border border-[#23C343]">
                      <Text className="text-[12px]  font-medium text-[#23C343]" style={{ lineHeight: 12 }}>
                        起点
                      </Text>
                    </View>
                  )}
                  {index === shopInfoDTOS?.length - 1 && (
                    <View className="flex h-4 w-8 items-center justify-center rounded-[2px] border border-[#F53F3F]">
                      <Text className="text-[12px] font-medium text-[#F53F3F]" style={{ lineHeight: 12 }}>
                        终点
                      </Text>
                    </View>
                  )}
                  <Text className="text-display-2sm font-medium text-[#1D2129]">{item?.shopName}</Text>
                </View>
                <TouchableOpacity
                  activeOpacity={1}
                  onPress={() => {
                    item?.longitude &&
                      item?.latitude &&
                      handGoToway({ location: [item.longitude, item.latitude], index });
                  }}
                >
                  <Icon name="gotoway" />
                </TouchableOpacity>
              </View>
              <Text className="mt-1 text-display-2xs text-[#86909C]">{item?.shopAddress}</Text>
              <View className="mb-[7px] mt-4 h-[1px] flex-1 bg-[#F0F0F0]" />
            </View>
          </View>
        );
      })}
    </View>
  );
};
