import { useEffect, useMemo } from 'react';
import { useIsFocused, useNavigation } from '@react-navigation/native';
import TstOm from '@src/components';
import { useCheckPermissionFn, usePermission } from '@src/hooks';
import {
  useQueryArriveTaskCount,
  useQueryRecruitDemandCount,
  useQueryTaskCountSum,
  useQueryTodayTaskCount,
  useQueryWaitAuditCount,
  useQueryWaitAuditTacticsCount,
} from '@src/http/service/supervisorTask';
import { useQueryFoodSafeAppealWaitAuditCount } from '@src/http/service/tactics/appeal';
import { supervisorPermission } from '@src/permission';
import { HomePermissionEnum, SchedulePermissionEnum } from '@src/permission/supervisor';
import { useGray } from '@src/store';
import Permission from '@src/ui/components/Permission';
import { Text, TouchableOpacity, View } from 'react-native';
import ShortcutButton from './ShortcutButton';
import { useRectCount } from './useRectCount';
import { dayTaskTypes, taskCountSumInitData } from '../constant';

export function TodayTask() {
  const isGrayUser = useGray((state) => state.isGrayUser);
  const { checkPermission } = usePermission();

  const selfTaskType: any = dayTaskTypes.find((m) => m.queryType === 'ONLY_SELF_TASK');
  const navigation = useNavigation<TstOm.StackParams.ScreenNavigationProp['navigation']>();
  // const { checkPermission } = usePermission();
  const { data: taskCountSum = taskCountSumInitData as any, refetch: todayTaskFetch } = useQueryTaskCountSum({
    queryKey: ['supervisorTaskCountSum'],
    gcTime: 0,
    staleTime: 0,
  });

  const { data: todayTaskSum, refetch: todayTaskCountFecth } = useQueryTodayTaskCount({
    queryKey: ['queryTodayTaskCount'],
    gcTime: 0,
    staleTime: 0,
  });

  const { data: todayArriveCount = 0, refetch: todayArriveCountFecth } = useQueryArriveTaskCount({
    queryKey: ['queryTodoArriveCount'],
    gcTime: 0,
    staleTime: 0,
  });

  const { data: recruitDemandCount = 0, refetch: recruitDemandCountFecth } = useQueryRecruitDemandCount({
    queryKey: ['recruitDemandCount'],
    gcTime: 0,
    staleTime: 0,
  });

  const { data: waitAuditCount = 0, refetch: waitAuditCountFecth } = useQueryWaitAuditCount({
    queryKey: ['waitAuditCount'],
    gcTime: 0,
    staleTime: 0,
  });

  const { data: waitAuditTacticsCount = 0, refetch: waitTacticsAuditCountFecth } = useQueryWaitAuditTacticsCount({
    queryKey: ['waitAuditTacticsCount'],
    gcTime: 0,
    staleTime: 0,
  });

  const { data: foodSafeAppealWaitAuditCount, refetch: foodSafeAppealWaitAuditCountFecth } =
    useQueryFoodSafeAppealWaitAuditCount({
      queryKey: ['foodSafeAppealWaitAuditCount'],
      gcTime: 0,
      staleTime: 0,
    });

  const rectCountTotal = useRectCount();

  const dealTaskCountSum = useMemo(() => {
    const sumData = { ...taskCountSum };
    const filterData = dayTaskTypes.filter((m) => m.permission && m.queryType !== 'ALL');

    // 计算所有有权限的数量
    let dayTaskCountSum = 0;

    filterData.forEach((n: any) => {
      dayTaskCountSum = dayTaskCountSum + sumData[n.field];
    });
    sumData.dayTaskCountSum = dayTaskCountSum;

    for (const key in sumData) {
      if (sumData.hasOwnProperty(key)) {
        const value = sumData[key];

        if (value > 99) {
          sumData[key] = '99+';
        }
      }
    }

    return sumData;
  }, [taskCountSum]);

  const isFocused = useIsFocused();

  console.log(todayTaskSum, '=todayTaskSum');

  useEffect(() => {
    if (isFocused) {
      todayTaskFetch();
      todayTaskCountFecth();
      todayArriveCountFecth();
      recruitDemandCountFecth();
      waitAuditCountFecth();
      waitTacticsAuditCountFecth();
      foodSafeAppealWaitAuditCountFecth();
    }
  }, [
    isFocused,
    todayTaskFetch,
    todayTaskCountFecth,
    todayArriveCountFecth,
    recruitDemandCountFecth,
    waitAuditCountFecth,
    waitTacticsAuditCountFecth,
    foodSafeAppealWaitAuditCountFecth,
  ]);

  // 今日待完成任务数量
  const { check } = useCheckPermissionFn();
  const todayTaskCount = useMemo(() => {
    const taskConfig = [
      {
        dataField: todayTaskSum?.patrolTaskCount ?? 0,
        permission: SchedulePermissionEnum['今日任务(新)_巡检任务'],
      },
      {
        dataField: todayTaskSum?.reviewReportCount ?? 0,
        permission: SchedulePermissionEnum['今日任务(新)_待点评'],
      },
    ];

    return taskConfig.reduce((total, { permission, dataField }) => {
      return check(permission) ? total + dataField : total;
    }, 0);
  }, [check, todayTaskSum?.patrolTaskCount, todayTaskSum?.reviewReportCount]);

  return (
    <View className="mt-3.5">
      <View className="mb-2 flex-row justify-between px-1">
        <View className="flex-row items-center gap-x-1">
          <TstOm.Icon name="today-task" className="fill-[#FFFFFF]" />
          <Text className="text-base font-medium text-base-black-2">今日待完成任务</Text>
        </View>
        <TouchableOpacity
          className="flex-row items-center gap-x-1.5"
          onPress={() => {
            navigation.navigate('Task', {
              queryType: 'ALL',
            });
          }}
        >
          {!!todayTaskCount && (
            <>
              <View className="rounded-full bg-[#CD3E36] px-1.5">
                <Text className="ml-px text-xs font-normal text-white">
                  {Number(todayTaskCount ?? 0) > 99 ? '99+' : todayTaskCount ?? 0}
                </Text>
              </View>

              <TstOm.Icon name="arrowRight" className="size-2.5 fill-[#B8B8B8]" />
            </>
          )}
        </TouchableOpacity>
      </View>
      <View className="rounded-[6px] bg-white px-2.5">
        {!!todayTaskCount && (
          <View className="flex-row items-center border-b border-b-gray-100 py-3.5 pl-1">
            <Text className="text-base-black-2">您有</Text>
            <Text className="font-medium text-base-black-2">{todayTaskCount}</Text>
            <Text className="text-base-black-2">项待处理任务未完成，请及时处理</Text>
          </View>
        )}
        {!!dealTaskCountSum.daySelfTaskCount && selfTaskType.permission && (
          <View>
            <View className="my-3.5 flex-row items-center justify-between pl-1.5">
              <Text className="text-base text-base-black-2">待点评</Text>
              <TouchableOpacity
                className="flex-row items-center gap-x-1.5"
                onPress={() => {
                  navigation.navigate('Task', {
                    queryType: 'ONLY_SELF_TASK',
                  });
                }}
              >
                <View className="flex-row gap-x-1">
                  <Text className="text-[#CD3E36]">{dealTaskCountSum.daySelfTaskCount}</Text>
                  <Text className="text-base-black-2">项待处理</Text>
                </View>
                <TstOm.Icon name="arrowRight" className="size-2.5 fill-[#B8B8B8]" />
              </TouchableOpacity>
            </View>
            <View className="flex-row justify-between gap-x-4 pb-3">
              <TstOm.LinearGradient
                className="flex-1 overflow-hidden rounded-[8px] px-3 py-3.5"
                colors={['#FFFDFB', '#FCF8EB']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <View className="flex-row items-center justify-between">
                  <Text className="text-[#5E5E5E]">门店数</Text>
                  <Text className="text-base font-medium text-base-black-2">
                    {dealTaskCountSum.daySelfTaskShopCount}
                  </Text>
                </View>
              </TstOm.LinearGradient>

              <TstOm.LinearGradient
                className="flex-1 overflow-hidden rounded-[8px] px-3 py-3.5"
                colors={['#FFFDFB', '#FCF8EB']}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              >
                <View className="flex-row items-center justify-between">
                  <Text className="text-[#5E5E5E]">报告数</Text>
                  <Text className="text-base font-medium text-base-black-2">{dealTaskCountSum.daySelfTaskCount}</Text>
                </View>
              </TstOm.LinearGradient>
            </View>
          </View>
        )}
        {dayTaskTypes
          .filter((m) => ['常规任务', '巡检任务', '稽核任务', '消杀任务'].includes(m.title) && m.permission)
          .map((item: any, index: number) => {
            if (!dealTaskCountSum[item.field]) {
              return null;
            }

            return (
              <View
                key={index}
                className="flex-row items-center justify-between border-t border-t-gray-100 py-4 pl-1.5"
              >
                <Text className="text-base text-base-black-2">{item.title}</Text>
                <TouchableOpacity
                  className="flex-row items-center gap-x-1.5"
                  onPress={() => {
                    navigation.navigate('Task', {
                      queryType: item.queryType,
                    });
                  }}
                >
                  <View className="flex-row gap-x-1">
                    <Text className="text-[#CD3E36]">{dealTaskCountSum[item.field]}</Text>
                    <Text className="text-base-black-2">项待处理</Text>
                  </View>
                  <TstOm.Icon name="arrowRight" className="size-2.5 fill-[#B8B8B8]" />
                </TouchableOpacity>
              </View>
            );
          })}
      </View>
      {!!todayArriveCount && checkPermission(HomePermissionEnum.食安稽核到店辅导任务) && (
        <ShortcutButton
          label="食安稽核到店辅导任务"
          count={todayArriveCount}
          onPress={() => {
            navigation.navigate('WebView', {
              url: 'tasks/list/principal/patrol?taskStatus=NOT_READY&subType=FOOD_SAFETY_ARRIVE_SHOP&queryType=principal&workType=patrol',
            });
          }}
        />
      )}
      {!!rectCountTotal && checkPermission(HomePermissionEnum.整改审核任务) && (
        <ShortcutButton
          label="整改待审核任务"
          count={rectCountTotal}
          onPress={() => {
            navigation.navigate('TaskRectification', {
              roleCategory: 'CORP',
            });
          }}
        />
      )}
      {/* {checkPermission('$app$recruit:audit') && ( */}
      <Permission permission={supervisorPermission.HomePermissionEnum.待审核招聘需求}>
        <ShortcutButton
          label="待审核招聘需求"
          count={recruitDemandCount}
          onPress={() => {
            navigation.navigate('WebView', {
              url: 'recruit/demand?tabKey=Pending',
            });
          }}
        />
      </Permission>
      <Permission permission={supervisorPermission.HomePermissionEnum.申诉待处理任务}>
        <ShortcutButton
          label="申诉待处理任务"
          count={waitAuditCount}
          onPress={() => {
            navigation.navigate('WebView', {
              url: 'complaint/waitForDeal',
            });
          }}
        />
      </Permission>
      <Permission permission={supervisorPermission.HomePermissionEnum['申诉待处理任务（新）']}>
        <ShortcutButton
          label="申诉待处理任务（新）"
          count={waitAuditTacticsCount}
          onPress={() => {
            navigation.navigate('WebView', {
              url: '/complaint/waitForDeal?isTactics=true',
            });
          }}
        />
      </Permission>
      <Permission permission={supervisorPermission.HomePermissionEnum.食安申诉待处理任务}>
        <ShortcutButton
          label="食安申诉待处理任务"
          count={foodSafeAppealWaitAuditCount?.data || 0}
          onPress={() => {
            navigation.navigate('FoodSafetyComplaint', {
              status: 'CREATE',
            });
          }}
        />
      </Permission>
    </View>
  );
}
