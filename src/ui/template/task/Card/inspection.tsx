/* eslint-disable max-lines */
import { memo, useCallback, useMemo, useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import TransferPersonSelectPopup from '@src/biz/supervisor/home/<USER>/transferApplyList-Strategy/components/transferPersonSelect';
import TransferReasonPopup from '@src/biz/supervisor/home/<USER>/transferApplyList-Strategy/components/transferReason';
import { StrategyTransferTag } from '@src/biz/supervisor/schedule/components/TaskPatrolItem/core';
import { showModal, showToast } from '@src/components/Components';
import { faceModule } from '@src/components/WebView/WebViewPlugin';
import { useRevocationAction } from '@src/hooks';
import { InspectionResponse, TaskStatus, TaskSubType, TaskTransferStatus } from '@src/http/service/task-center';
import { ShopStatusEnum } from '@src/http/service/taskCenter';
import { useMutationCreateTransfer, usePatrolTaskStartTime } from '@src/http/service/transfer-Strategy';
import { useAuth, useGray, user } from '@src/store';
import useConfig from '@src/store/useConfig';
import { Button, Icon, Text as RNText, Toast } from '@src/ui/components/components';
import { cn, encrypt, ShopType, SignTypeText } from '@src/utils';
import dayjs from 'dayjs';
import { isBoolean } from 'lodash';
import { Linking, Text, TouchableOpacity, View } from 'react-native';
import LevelTag from '../../diagnosis/LevelTag';
import { CheckWayCNToEnEnum } from '../interface';

enum EBtn {
  签到,
  签离,
  签到并巡店,
  巡店,
  查看报告,
  撤回,
  去点评,
}

export enum ESign {
  不需要签到,
  需要签到,
  需要签到签离,
}

enum TaskTypeEnum {
  到店巡检 = 'NORMAL',
  诊断任务 = 'DIAGNOSTIC',
}

const StatusTextMap = {
  [TaskStatus.已提交]: '已提交',
  [TaskStatus.进行中]: '进行中',
  [TaskStatus.待开始]: '待开始',
  [TaskStatus.已完成]: '已完成',
  [TaskStatus.已作废]: '已作废',
  [TaskStatus.已逾期]: '已过期',
  [TaskStatus.逾期进行中]: '逾期进行中',
  [TaskStatus.转办审核中]: '转办审核中',
  [TaskStatus.待确认到店]: '待确认到店',
};

type TProps = InspectionResponse &
  Partial<{
    /** 是否显示头部状态 */
    isShowHeadStatus: boolean;
    /** 显示巡检时间 */
    isShowTime?: boolean;
    /** 是否显示执行截止时间 */
    isShowCarryOutEndTime?: boolean;
    /** 是否显示拨打电话 */
    isShowCallTelephone?: boolean;
    /** 是否显示地址 */
    isShowAddress?: boolean;
    /** 是否显示门店id 默认显示 */
    isShowShopId?: boolean;
    /** 是否显示门店类型 */
    isShowShopType?: boolean;
    handleTransfer: (id: number, isNewInspector?: boolean) => void;
    /** 是否二方稽核卡片 */
    isSecondAudit?: boolean;
    /** 是否超管 */
    isSuperManger?: boolean;
    /** 执行开始时间 不传默认取原先 beginTime 字段 */
    carryOutStartTime?: string;
  }>;

function Inspection({
  isShowHeadStatus = true,
  items = [],
  className,
  isShowTime = true,
  isShowCarryOutEndTime,
  onRefresh,
  isShowCallTelephone = true,
  isShowAddress = true,
  isShowShopId = true,
  isShowShopType = true,
  isSecondAudit = false,
  isSuperManger, // 是否是超级管理员
  taskSubType,
  carryOutStartTime,
  hasExpired,
  ...data
}: TProps & { className?: string }) {
  const role = useAuth((state) => state.currentRole);
  const { isGaryUser, isGaryShop } = useGray((state) => ({
    isGaryUser: state.isGrayUser,
    isGaryShop: state.isGrayShop,
  }));

  console.log(isSuperManger, '=isSuperManger');

  const navigation = useNavigation<TstOm.StackParams.ScreenNavigationProp['navigation']>();
  const { config } = useConfig();
  /** 系统的签到配置 */
  const systemCheckInConfig = +config?.MUST_CHECK_IN as ESign;
  const [hide, setHide] = useState<boolean>(true);
  const [transferReasonInfo, setTransferReasonInfo] = useState<{ taskId: number | undefined; visible: boolean }>({
    taskId: undefined,
    visible: false,
  });
  const [transferPersonInfo, setTransferPersonInfo] = useState<{ taskId: number | undefined; visible: boolean }>({
    taskId: undefined,
    visible: false,
  });

  const { mutateAsync: patrolTaskMutateAsync } = usePatrolTaskStartTime();

  const [newInspector, setNewInspector] = useState<number>();

  // 未开始 禁用
  const notStartedDisabled = useMemo(() => {
    if (data?.taskTag === 'ROUTE_BATCH_FOOD_SAFETY_NORMAL') {
      return false;
    }

    const start = data?.startTime || data?.beginTime;

    return !!start && dayjs().isBefore(start);
  }, [data?.beginTime, data?.startTime, data?.taskTag]);

  const batchList = useMemo(() => {
    return hide ? items?.slice(0, 2) : items;
  }, [items, hide]);

  const ids = useMemo(
    () =>
      items
        ?.filter((v) => {
          return v?.applicationStatus !== TaskTransferStatus.转办待审核;
        })
        .map((v) => v?.id),
    [items],
  );

  const isNeedSign = useMemo(() => {
    const filterData = items?.filter((v) => {
      return v?.applicationStatus !== TaskTransferStatus.转办待审核;
    });
    const hasSignTask = filterData?.some((v) => {
      const { signType } = v;
      const isSignType = [SignTypeText.签到, SignTypeText.签到签离].includes(signType as SignTypeText);

      const isFollowSystem = signType === SignTypeText.跟随系统 && [1, 2].includes(systemCheckInConfig);

      // 项中存在一项需要签到且待开始的，则展示签到
      return (isSignType || isFollowSystem) && v.taskStatus === 'WAITING_START';
    });

    return hasSignTask;
  }, [systemCheckInConfig, items]);

  const { mutateAsync: handleCreateTransfer } = useMutationCreateTransfer({
    onSuccess: () => {
      showToast({ title: '申请成功', icon: 'none' });
      setTimeout(() => {
        onRefresh && onRefresh();
      }, 200);
    },
    onError: (error: any) => {
      console.log('error :>> ', error);

      throw new Error(`申请失败: ${error}`);
    },
  });

  /**
   * 计算权限按钮
   */
  const btnList = useMemo(() => {
    const { hasSigned, hasLeaved, signType, status, hasNeedReview, taskTag } = data;

    if (hasNeedReview) {
      return [EBtn.去点评];
    }

    if (isNeedSign) {
      return [EBtn.签到];
    }

    if ([CheckWayCNToEnEnum['食安线上稽核'], CheckWayCNToEnEnum['视频巡检']].includes(taskSubType as any)) {
      return [EBtn.撤回];
    }

    // 根据任务状态和签到类型的组合来返回按钮
    const getButtonsForStatus = () => {
      switch (status) {
        case TaskStatus.待开始: {
          switch (signType) {
            case SignTypeText.跟随系统:
              if (systemCheckInConfig === ESign.不需要签到) {
                return [EBtn.签到, EBtn.巡店];
              } else if (systemCheckInConfig === ESign.需要签到 || systemCheckInConfig === ESign.需要签到签离) {
                return [EBtn.签到并巡店];
              }

              break;

            case SignTypeText.不需要签到:
              return [EBtn.签到, EBtn.巡店];

            case SignTypeText.签到:
            case SignTypeText.签到签离:
              return [EBtn.签到并巡店];
          }

          break;
        }

        case TaskStatus.逾期进行中:
        case TaskStatus.进行中: {
          if (status === TaskStatus.逾期进行中 && taskSubType !== 'FOOD_SAFETY_NORMAL') {
            return [];
          }

          if (signType === SignTypeText.跟随系统) {
            if (systemCheckInConfig === ESign.不需要签到) {
              if (!hasSigned) {
                return [EBtn.签到, EBtn.巡店];
              } else if (hasSigned) {
                return [EBtn.签离, EBtn.巡店];
              }
            } else if (systemCheckInConfig === ESign.需要签到 || systemCheckInConfig === ESign.需要签到签离) {
              return [EBtn.巡店];
            }
          }

          if (signType === SignTypeText.不需要签到) {
            if (!hasSigned) {
              return [EBtn.签到, EBtn.巡店];
            } else if (hasSigned) {
              return [EBtn.签离, EBtn.巡店];
            }
          }

          if (signType === SignTypeText.签到 || signType === SignTypeText.签到签离) {
            return [EBtn.巡店];
          }

          if (signType === SignTypeText.不需要签到 && hasLeaved) {
            return [EBtn.巡店];
          }

          break;
        }

        case TaskStatus.已完成:
        case TaskStatus.已提交:
          return [EBtn.撤回, EBtn.查看报告];

        default:
          return [];
      }
    };

    return getButtonsForStatus();
  }, [data, isNeedSign, systemCheckInConfig, taskSubType]);

  const { onRevocation } = useRevocationAction({
    url: isSecondAudit
      ? '/tm-api/supplier/task/revocation-base-task'
      : isSuperManger
        ? '/tm-api/corp/sa/task/revocation-base-task'
        : '/tm-api/corp/task/revocation-base-task',
  });

  const makePhoneCall = useCallback(async (phone: string) => {
    try {
      if (!phone) {
        Toast({
          type: 'text',
          message: '暂无手机号',
        });

        return;
      }

      await Linking.openURL(`tel:${phone}`);
    } catch (error) {
      console.log('error :>> ', error);
    }
  }, []);

  // 按钮映射
  const BtnMap: Record<EBtn, JSX.Element> = {
    [EBtn.签到]: (
      <Button
        key={EBtn.签到}
        onPress={() => {
          console.log('🚀 ~ data:', data);

          if (isSecondAudit) {
            navigation.navigate('SecondSupplierSignIn', {
              taskId: data?.id,
              shopId: data?.shopId,
            });
          } else {
            faceModule.navigateTo({
              param: encrypt({
                shopId: data.shopId,
                taskId: items.length ? ids : data?.id ?? data?.taskId,
                isNew: true,
                selectionCompleted: false,
                selectorNumber: items.length ? items[0]?.mustCount : data?.mustCount,
                path: 'SupervisorSignIn',
                callNativeBackUrl: 'SupervisorStrategyTask',
                isGrayUser: isGaryUser,
              }),
              native: true,
            });
          }
        }}
        className=" border border-primary"
        variant="outline"
        size="sm"
        disabled={notStartedDisabled}
      >
        <RNText className="color-primary">签到</RNText>
      </Button>
    ),
    [EBtn.去点评]: (
      <Button
        key={EBtn.去点评}
        onPress={() => {
          if (data?.mustCount > 0) {
            navigation.navigate(
              'SupervisorOptions',
              encrypt({
                taskId: data?.taskId, // 批量签到
                callNativeBackUrl: 'StrategyTaskReview',
                selectionCompleted: false,
                selectorNumber: data?.mustCount,
                hasReview: data?.hasNeedReview,
                isGrayUser: role?.roleCategory === '2' ? isGaryShop : isGaryUser,
              }),
            );

            return;
          }

          navigation.navigate('StrategyTaskReview', {
            taskId: data?.taskId,
            hasReview: data?.hasNeedReview,
          });
        }}
        className="border border-primary"
        variant="outline"
        size="sm"
      >
        <RNText className="color-primary">去点评</RNText>
      </Button>
    ),
    [EBtn.签离]: (
      <Button
        key={EBtn.签离}
        onPress={() => {
          if (isSecondAudit) {
            navigation.navigate('SecondSupplierSignOut', {
              taskId: data?.id,
              shopId: data?.shopId,
            });
          } else {
            faceModule.navigateTo({
              param: encrypt({
                shopId: data.shopId,
                taskId: data?.id,
                isNew: true,
                path: 'SupervisorCheckout',
                isGrayUser: isGaryUser,
              }),
              native: true,
            });
          }
        }}
        className=" border border-primary"
        variant="outline"
        size="sm"
      >
        <RNText className="color-primary">签离</RNText>
      </Button>
    ),
    [EBtn.签到并巡店]: (
      <Button
        key={EBtn.签到并巡店}
        onPress={() => {
          if (isSecondAudit) {
            navigation.navigate('SecondSupplierSignIn', {
              taskId: data?.id,
              shopId: data?.shopId,
            });
          } else {
            // 埋点
            patrolTaskMutateAsync({ taskId: Number(data?.id) });

            faceModule.navigateTo({
              param: encrypt({
                shopId: data.shopId,
                taskId: data.id,
                path: 'SupervisorSignIn',
                selectionCompleted: false,
                selectorNumber: data?.mustCount,
                isGrayUser: isGaryUser,
                isNew: true,
                callNativeBackUrl: 'SupervisorStrategyTask',
              }),
            });
          }
        }}
        className=" border border-primary"
        variant="outline"
        size="sm"
        disabled={notStartedDisabled}
      >
        <RNText className="color-primary">签到并执行</RNText>
      </Button>
    ),
    [EBtn.巡店]: (
      <Button
        key={EBtn.巡店}
        size="sm"
        onPress={() => {
          if (data?.mustCount > 0 && !isSecondAudit) {
            // 二方稽核不用选择检查表
            navigation.navigate(
              'SupervisorOptions',
              encrypt({
                taskId: data?.id || data?.taskId, // 批量签到
                selectionCompleted: false,
                hasReview: data?.hasNeedReview,
                selectorNumber: data?.mustCount,
                isGrayUser: role?.roleCategory === '2' ? isGaryShop : isGaryUser,
                callNativeBackUrl: 'SupervisorStrategyTask',
              }),
            );

            return;
          }

          // 埋点
          patrolTaskMutateAsync({ taskId: Number(data?.id) });

          navigation.navigate('SupervisorStrategyTask', {
            taskId: data?.id,
          });
        }}
        className=" border border-primary"
        variant="outline"
        disabled={notStartedDisabled}
      >
        <RNText className="color-primary">执行</RNText>
      </Button>
    ),
    [EBtn.查看报告]: (
      <Button
        key={EBtn.查看报告}
        size="sm"
        onPress={() => {
          navigation.navigate('StrategyTaskDetail', {
            taskId: data?.id,
          });
        }}
        className=" border border-primary"
        variant="outline"
      >
        <RNText className="color-primary">查看报告</RNText>
      </Button>
    ),
    [EBtn.撤回]: (
      <Button
        key={EBtn.撤回}
        size="sm"
        onPress={() => onRevocation(data?.id as any)}
        className=" border border-[#DCDCDC]"
        variant="outline"
      >
        <RNText className="text-[#5E5E5E]">撤回</RNText>
      </Button>
    ),
  };

  const { integerPart, decimalPart } = useMemo(() => {
    const score = data?.diagnosticWeightScore?.toFixed(2) || '0.00'; // 保留两位小数
    const [integer, decimal] = score.split('.'); // 分割整数和小数

    return {
      integerPart: integer,
      decimalPart: decimal,
    };
  }, [data]);

  // 交接人信息
  // 任务中心的项是单个任务的数据，所以直接取data中的字段，今日任务和待办任务是按打包任务的格式展示数据，因此要从items里面取第一项（交接任务不打包，items长度必须为1）
  const { handoverParty, receivingParty } = useMemo(() => {
    let handover, receiving;

    if (data?.handoverParty) {
      handover = data?.handoverParty;
    } else {
      handover = items?.[0]?.handoverParty;
    }

    if (data?.receivingParty) {
      receiving = data?.receivingParty;
    } else {
      receiving = items?.[0]?.receivingParty;
    }

    return { handoverParty: handover, receivingParty: receiving };
  }, [data, items]);

  return (
    <View className={cn('mt-2.5 rounded-lg bg-white px-3 py-2', className)}>
      {isShowHeadStatus && (
        <View className="flex-row items-center justify-between">
          {data?.taskSubType === TaskSubType.Diagnostic ? (
            <View className="flex-row items-center">
              <LevelTag className="relative right-3 w-24" isGrayUser level={data?.alarmLevel} />
              <Text className="relative -left-4 text-[12px] font-normal text-[#BEBEBE]">（诊断任务）</Text>
            </View>
          ) : null}
          <Text
            className={cn('text-[14px] font-normal text-primary', {
              'text-[#6CCD5F]': data.status === TaskStatus.进行中,
              'text-[#B8B8B8]': data.status === TaskStatus.已作废 || data.status === TaskStatus.已逾期,
              'text-[#FFAE4E]': data.status === TaskStatus.逾期进行中,
            })}
          >
            {data.status === TaskStatus.已完成 && hasExpired
              ? '逾期已完成'
              : StatusTextMap[data.status as keyof typeof StatusTextMap]}
          </Text>
        </View>
      )}
      <View className="gap-y-2 py-2">
        <View className="flex-row items-center justify-between">
          <View className="flex flex-row gap-2">
            <Text className="text-[14px] font-medium text-[#141414]">
              {isShowShopId ? data?.shopId : null} {data.shopName}
            </Text>
            {isShowShopType && data.shopType ? (
              <Text className="text-[13px] font-normal text-[#9C9C9C]">
                {`(${ShopType[data.shopType as keyof typeof ShopType]})`}
              </Text>
            ) : null}
            {data?.shopStatus ? (
              <Text className="text-[13px] font-normal text-[#9C9C9C]">{`(${ShopStatusEnum?.[data?.shopStatus as any]})`}</Text>
            ) : null}
            {isBoolean(data?.mustFlag) ? (
              <Text className={cn('ml-1 text-sm font-normal leading-5', { 'text-[#d23127]': data?.mustFlag })}>
                {data?.mustFlag ? '必检' : '非必检'}
              </Text>
            ) : null}
          </View>

          {isShowCallTelephone && (
            <TouchableOpacity onPress={() => makePhoneCall(data?.shopManagerPhoneNumber || data?.shopPhone)}>
              <Icon name="telephone" className="fill-primary" />
            </TouchableOpacity>
          )}
        </View>
        {isShowTime ? (
          <Text className="text-[12px] text-[#858585]">
            执行时段：{dayjs(carryOutStartTime ?? data.beginTime).format('YYYY/MM/DD HH:mm')}-
            {dayjs(data.expiredTime).format('YYYY/MM/DD HH:mm')}
          </Text>
        ) : null}
        {data?.hikvisionUserName && (
          <Text className="text-[12px] font-normal text-[#767676]">
            被鉴定人：{data?.hikvisionUserName}({data?.hikUserFeishuNo})
          </Text>
        )}
        {data?.diagnosticWeightScore && data?.taskSubType === TaskSubType.Diagnostic ? (
          <View className="flex-row items-center justify-between">
            <Text className="text-[12px] font-normal text-[#858585]">检查得分</Text>
            <Text className="flex-row items-end">
              <Text className="text-[24px] font-medium">{integerPart}</Text>
              <Text className="self-start text-[14px] font-medium">.{decimalPart}</Text>
            </Text>
          </View>
        ) : null}
        {data?.taskType === 'REVIEW' && <Text className="text-[12px] text-[#858585]">{data?.taskName}</Text>}
        {isShowCarryOutEndTime && (
          <Text className="text-[12px] text-[#858585]">
            执行截止时间：{dayjs(data.expiredTime).format('YYYY/MM/DD HH:mm:ss')}
          </Text>
        )}
        {isShowAddress && (
          <View className="flex-row items-start gap-x-1">
            <Icon name="location" className="fill-[#5E5E5E]" />
            <View className="flex-1">
              <Text className="font-normal text-[#5E5E5E]">{data.shopAddress}</Text>
            </View>
          </View>
        )}
        {handoverParty && receivingParty ? (
          <Text className="text-sm font-normal text-[#141414]">
            门店由{handoverParty?.nickname}（{handoverParty?.phone}）转至{receivingParty?.nickname}（
            {receivingParty?.phone}）
          </Text>
        ) : undefined}
      </View>
      {batchList.length > 0 && (
        <View className="mb-2 mt-4 gap-y-2 px-1.5">
          {batchList.map((item) => (
            <View className="w-full flex-col">
              <View key={item.id} className="flex-row items-center justify-between">
                <View className="mr-15 flex-1 flex-row items-center gap-x-2">
                  {StrategyTransferTag[item?.taskStatus]}
                  <Text numberOfLines={2} className="text-[12px] font-semibold text-[#333333]">
                    {item.taskName}
                  </Text>
                </View>

                <View className="flex-row gap-x-4">
                  {item?.applicationStatus !== TaskTransferStatus.转办待审核 &&
                    item?.taskStatus === TaskStatus.待开始 &&
                    item?.hasTransfer && (
                      <Button
                        variant="link"
                        onPress={() => {
                          if (item?.supportChooseTransfer) {
                            if (item?.taskSubType === TaskTypeEnum.诊断任务) {
                              navigation.navigate('TransferPersonnelSelect', {
                                taskId: item?.id,
                                filterUserId: user?.shUserId,
                                isGrayUser: true,
                              });
                            } else {
                              setTransferPersonInfo({
                                taskId: item.id,
                                visible: true,
                              });
                            }
                          } else {
                            setTransferReasonInfo({
                              taskId: item.id,
                              visible: true,
                            });
                          }
                        }}
                      >
                        <Text className={cn('text-[14px] font-normal text-primary')}>转办</Text>
                      </Button>
                    )}

                  <Button
                    disabled={
                      isNeedSign ||
                      item?.applicationStatus === TaskTransferStatus.转办待审核 ||
                      item?.applicationStatus === TaskTransferStatus.待接受 ||
                      notStartedDisabled
                    }
                    variant="link"
                    onPress={() => {
                      if ((item?.mustCount || 0) > 0) {
                        navigation.navigate(
                          'SupervisorOptions',
                          encrypt({
                            taskId: item?.id, // 批量签到
                            selectionCompleted: false,
                            hasReview: item?.hasNeedReview,
                            selectorNumber: item?.mustCount,
                            isGrayUser: role?.roleCategory === '2' ? isGaryShop : isGaryUser,
                            callNativeBackUrl: 'SupervisorStrategyTask',
                          }),
                        );

                        return;
                      }

                      // 埋点
                      patrolTaskMutateAsync({ taskId: item?.id });

                      // navigation.navigate('PatrolTaskDetailStrategy', {
                      //   taskId: item.id,
                      // });
                      navigation.navigate('SupervisorStrategyTask', {
                        taskId: item.id,
                      });
                    }}
                  >
                    <Text className={cn('text-[14px] font-normal text-primary')}>执行</Text>
                  </Button>
                </View>
              </View>
              {item?.hikvisionUserName && (
                <View className="py-1">
                  <Text className="text-[12px] font-semibold text-[#858585]">
                    被鉴定人：{item.hikvisionUserName}({item.hikUserFeishuNo})
                  </Text>
                </View>
              )}
            </View>
          ))}
        </View>
      )}
      {items?.length > 2 && (
        <View className="mt-2 flex-row justify-center">
          {hide ? (
            <TouchableOpacity
              className="flex-row items-center gap-x-1"
              activeOpacity={0.8}
              onPress={() => setHide(false)}
            >
              <Text className="font-normal text-[#B8B8B8]">展开全部</Text>
              <Icon name="arrowDown" className="size-3 fill-[#B8B8B8]" />
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              className="flex-row items-center gap-x-1"
              activeOpacity={0.8}
              onPress={() => setHide(true)}
            >
              <Text className="font-normal text-[#B8B8B8]">收起</Text>
              <Icon name="arrowUp" className="size-3 fill-[#B8B8B8]" />
            </TouchableOpacity>
          )}
        </View>
      )}

      <View className="mt-2 flex-row items-center justify-end gap-x-2">
        {btnList?.map((item) => BtnMap[item as keyof typeof BtnMap])}
      </View>

      {transferReasonInfo.visible && (
        <TransferReasonPopup
          title="任务转办理由"
          visible={transferReasonInfo.visible}
          onClose={() =>
            setTransferReasonInfo({
              taskId: undefined,
              visible: false,
            })
          }
          placeholder="请填写任务转办理由，最多150个字"
          onConfirm={async (reason) => {
            if (!reason?.length) {
              return;
            }

            await handleCreateTransfer({
              reason,
              taskId: transferReasonInfo?.taskId!,
              newInspector: newInspector || undefined,
            });
          }}
        />
      )}
      {transferPersonInfo.visible && (
        <TransferPersonSelectPopup
          title="请选择转办人员"
          visible={transferPersonInfo.visible}
          taskId={transferPersonInfo?.taskId?.toString()}
          onClose={() =>
            setTransferPersonInfo({
              taskId: undefined,
              visible: false,
            })
          }
          onSelect={(v) => {
            showModal({
              title: '提示',
              content: `确定指派${v?.nickname}？`,
              success: async (res) => {
                if (res.confirm) {
                  await setNewInspector(v?.userId);
                  console.log(transferPersonInfo, '=transferPersonInfo');

                  await setTransferReasonInfo({
                    taskId: transferPersonInfo.taskId,
                    visible: true,
                  });
                  await setTransferPersonInfo({
                    taskId: undefined,
                    visible: false,
                  });
                }
              },
            });
          }}
        />
      )}
    </View>
  );
}

export default memo(Inspection);
