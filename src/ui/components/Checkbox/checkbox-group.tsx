import { memo } from 'react';
import { cn } from '@src/utils';
import omit from 'lodash/omit';
import { ScrollView, View } from 'react-native';
import Checkbox from './checkbox';
import type { CheckboxGroupProps } from './interface';
import { useControllableValue } from '../hooks';

function CheckboxGroup<T = any>({
  options,
  multiple,
  editable = true,
  scrollable = false,
  deselect = true,
  checkboxLabelTextClassName,
  ...restProps
}: CheckboxGroupProps<T>) {
  const [value, onChange] = useControllableValue<T | T[] | undefined | null>(restProps, {
    defaultValue: multiple ? [] : undefined,
  });

  const contentJSX = (
    <View
      className={cn('flex-col', {
        'flex-row gap-x-2': restProps.direction === 'horizontal',
        'flex-nowrap': !restProps.wrap,
        'flex-wrap': restProps.wrap,
      })}
      {...omit(restProps, ['value', 'defaultValue', 'onChange'])}
    >
      {options.map(({ value: checkboxValue, ...checkboxProps }) => {
        const selected = multiple ? (value as T[])?.indexOf(checkboxValue) > -1 : value === checkboxValue;

        return (
          <Checkbox
            {...checkboxProps}
            labelTextClassName={restProps.labelTextClassName ?? checkboxLabelTextClassName}
            key={`${checkboxValue}`}
            activeValue={checkboxValue}
            inactiveValue={null}
            value={selected ? checkboxValue : null}
            onChange={(_value) => {
              if (!editable) {
                return;
              }

              const isReset = _value !== checkboxValue;

              if (multiple) {
                const oldValue = (value as T[]) ?? [];
                const newValue = isReset ? oldValue.filter((v) => v !== checkboxValue) : [checkboxValue, ...oldValue];
                const newOptions = newValue.map((v) => {
                  const optionIndex = options.findIndex((o) => o.value === v);

                  return {
                    ...options[optionIndex],
                  };
                });

                onChange(newValue, newOptions);
              } else {
                if (!isReset || (isReset && deselect)) {
                  const newValue = isReset ? undefined : _value;
                  const newOptions = isReset ? undefined : options.filter((o) => o.value === _value);

                  onChange(newValue, newOptions);
                }
              }
            }}
          />
        );
      })}
    </View>
  );

  if (scrollable && restProps.direction === 'horizontal' && !restProps.wrap) {
    return (
      <ScrollView horizontal bouncesZoom={false} showsHorizontalScrollIndicator={false}>
        {contentJSX}
      </ScrollView>
    );
  }

  return contentJSX;
}

export default memo(CheckboxGroup) as <ActiveValueT = any>(p: CheckboxGroupProps<ActiveValueT>) => JSX.Element;
