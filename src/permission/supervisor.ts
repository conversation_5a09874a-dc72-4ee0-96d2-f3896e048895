/** 首页权限枚举 */
enum HomePermissionEnum {
  派发诊断任务 = '$app$distribute$diagnose$report',
  派发转办审核 = '$app$diagnose$transfer$check',
  待审核招聘需求 = '$app$recruit:audit',
  申诉待处理任务 = '$app$complaint:waitForDeal',
  '申诉待处理任务（新）' = '$app$complaint:waitForDeal:new',
  任务转办申请 = '$app$task$transfer$application',
  任务转办申请_诊断任务权限外转派 = '$app$task$transfer$application$diagnosis$permission$out$transfer',
  任务转办申请_到店巡检权限外转派 = '$app$task$transfer$application$inspection$permission$out$transfer',
  // -----------------
  任务转办申请_执行时段确认 = '$app$supervisor$task$transfer$application$execute$time$confirm',
  消息 = '$app$supervisor$notice$center',
  食安稽核到店辅导任务 = '$app$supervisor$food$safety$store$coach$task',
  食安稽核到店辅导任务_执行时段确认 = '$app$supervisor$food$safety$store$coach$task$execute$time$confirm',
  食安稽核到店辅导任务_食安稽核到店辅导任务 = '$app$supervisor$food$safety$store$coach$task$list',
  食安稽核到店辅导任务_任务执行时段修改申请 = '$app$supervisor$food$safety$store$coach$task$execute$time$confirm$apply',
  食安稽核到店辅导任务_任务执行人员调整申请 = '$app$supervisor$food$safety$store$coach$task$execute$person$adjustment$apply',
  整改审核任务 = '$app$supervisor$rectify$check',
  诊断周报 = '$app$supervisor$diagnose$week$report',
  '诊断周报(新)' = '$app$supervisor$diagnose$week$report$new',
  人脸审核待办 = '$app$supervisor$face$check$todo',
  '自检&巡检报告点评结果提醒' = '$app$supervisor$self$inspection$report$comment$result$remind',
  差异项审核 = '$app$supervisor$difference$check',
  '差异项审核(新)' = '$app$supervisor$difference$check$new',
  '差异项三方审核(新)' = '$app$supervisor$difference$check$third$new',
  食安申诉待处理任务 = '$app$supervisor$complaint$wait$for$deal',
}

/** 日程权限枚举 */
enum SchedulePermissionEnum {
  任务中心 = 'dataBoard:tasks',
  任务中心_我处理的 = 'dataBoard:tasks:categorize:principal',
  任务中心_我处理的_常规任务 = 'dataBoard:tasks:categorize:principal:normal',
  任务中心_我处理的_巡检任务 = 'dataBoard:tasks:categorize:principal:patrol',
  任务中心_我处理的_消杀任务 = 'dataBoard:tasks:categorize:principal:structKill',
  任务中心_我处理的_任务中心_我处理的_消杀任务_消杀任务分配 = '$app$supervisor$schedule$task$center$my$handle$disinfection$task$allot',
  // -----------------
  任务中心_我派发的 = 'dataBoard:tasks:categorize:created',
  任务中心_我派发的_常规任务 = 'dataBoard:tasks:categorize:created:normal',
  任务中心_我派发的_紧急消杀 = 'dataBoard:tasks:emergencyStructKill',
  // -----------------
  任务中心_抄送我的 = 'dataBoard:tasks:categorize:recipients',
  任务中心_抄送我的_常规任务 = 'dataBoard:tasks:categorize:recipients:normal',
  // -----------------
  任务中心_我管辖的 = 'dataBoard:tasks:categorize:under',
  任务中心_我管辖的_常规任务 = 'dataBoard:tasks:categorize:under:normal',
  任务中心_我管辖的_自检任务 = 'dataBoard:tasks:categorize:under:selfPatrol',
  任务中心_我管辖的_巡检任务 = 'dataBoard:tasks:categorize:under:patrol',
  任务中心_我管辖的_消杀任务 = 'dataBoard:tasks:categorize:under:structKill',
  // -----------------
  计划管理 = '$app$planManagement',
  计划管理_创建计划 = '$app$planManagement$create',
  计划管理_创建计划_到店巡检 = '$app$planManagement$create$normal',
  计划管理_创建计划_视频云巡检 = '$app$planManagement$create$video',
  计划管理_创建计划_食安线下稽核 = '$app$planManagement$create$FOOD_SAFETY_NORMAL',
  // -----------------
  // 报告中心 = 'selftask$reportcenter$view',
  报告中心_自检报告 = 'app$report$selftask',
  报告中心_自检报告_申诉 = 'app$report$selftask$appeal',
  报告中心_巡检报告 = 'app$report$patrol',
  报告中心_巡检报告_食安点评 = 'report:food:safety',
  报告中心_巡检报告_巡检点评 = 'report:routineInspection:report:comment',
  报告中心_消杀报告 = 'app$report$disinfection',
  // -----------------
  创建任务 = 'dataBoard:tasks:create:crop',
  创建任务_常规任务 = 'dataBoard:tasks:create:normal:crop',
  创建任务_到店巡检计划外任务 = 'dataBoard:tasks:create:patrol:unplanned:crop',
  创建任务_稽核任务 = 'dataBoard:tasks:create:patrol:food:crop',
  创建任务_紧急消杀任务 = 'dataBoard:tasks:create:disinfect:crop',
  // -----------------
  '今日&待办任务_巡检任务' = '$app$patroltask',
  '今日&待办任务_巡检第一待点评' = 'app$report$PatrolFirstAwaitComment',
  '今日&待办任务_巡检第二待点评' = 'app$report$PatrolSecondAwaitComment',
  // -----------------
  '今日任务(新)' = '$app$supervisor$schedule$task$center$today$new',
  '今日任务(新)_待点评' = '$app$supervisor$schedule$task$center$today$new$pending$review',
  '今日任务(新)_巡检任务' = '$app$supervisor$schedule$task$center$today$new$inspection$task',
  '待办任务(新)' = '$app$supervisor$schedule$task$center$await$new',
  '待办任务(新)_巡检任务' = '$app$supervisor$schedule$task$center$await$new$inspection$task',
  '待办任务(新)_点评任务' = '$app$supervisor$schedule$task$center$await$new$review$task',
  '任务中心(新)' = '$app$supervisor$schedule$task$center$new',
  '报告中心(新)' = '$app$supervisor$schedule$report$center',
  '报告中心(新)_自检报告' = '$app$supervisor$schedule$report$center$new$selfTask',
  '报告中心(新)_巡检报告' = '$app$supervisor$schedule$report$center$new$patrol',
  '报告中心(新)_巡检报告_申诉' = '$app$supervisor$schedule$report$center$new$patrol$appeal',

  '食安申诉报告' = '$app$supervisor$schedule$complaint$report',

  '待办任务(新)_稽核计划' = '$app$supervisor$schedule$task$center$await$new$audit$plan',
}

/**
 * 报告中心入口权限 有任意一个子集权限就展示入口
 */
const reportEntrancePermissions = [
  SchedulePermissionEnum.报告中心_自检报告,
  SchedulePermissionEnum.报告中心_巡检报告,
  SchedulePermissionEnum.报告中心_巡检报告_食安点评,
  SchedulePermissionEnum.报告中心_巡检报告_巡检点评,
  SchedulePermissionEnum.报告中心_消杀报告,
];

/** 我的权限枚举 */
enum MyPermissionEnum {
  视频监控 = 'home$video$view',
  图库管理 = '$app$supervisor$home$picture$library$view',
  组织架构 = '$app$supervisor$home$organization$view',
  人脸管理 = '$app$supervisor$home$face$management$view',
  问题跟踪整改 = '$app$supervisor$home$rectification$task$manage',
  问题跟踪整改_门店自检整改任务 = '$app$supervisor$home$store$self$check$rectification$task$view',
  问题跟踪整改_到店巡检整改任务 = '$app$supervisor$home$inspection$rectification$task$view',
  问题跟踪整改_视频巡检整改任务 = '$app$supervisor$home$video$inspection$rectification$task$view',
  问题跟踪整改_AI巡检整改任务 = '$app$supervisor$home$ai$inspection$rectification$task$view',
  问题跟踪整改_食安线上稽核整改任务 = '$app$supervisor$home$food$safety$online$auditing$rectification$task$view',
  问题跟踪整改_食安线下稽核整改任务 = '$app$supervisor$home$$food$safety$offline$auditing$rectification$task$view',
  问题跟踪整改_消杀整改任务 = '$app$supervisor$home$disinfection$rectification$task$view',
  '问题跟踪整改_门店自检整改任务(新)' = '$app$supervisor$home$store$self$check$rectification$task$view$new',
  '问题跟踪整改_到店巡检整改任务(新)' = '$app$supervisor$home$inspection$rectification$task$view$new',
  '问题跟踪整改_视频巡检整改任务(新)' = '$app$supervisor$home$video$inspection$rectification$task$view$new',
  '问题跟踪整改_诊断整改任务(新)' = '$app$supervisor$home$diagnosis$rectification$task$view$new',
  '问题跟踪整改_食安线上稽核整改任务(新)' = '$app$supervisor$home$food$safety$online$auditing$rectification$task$view$new',
  '问题跟踪整改_食安线下稽核整改任务(新)' = '$app$supervisor$home$food$safety$offline$auditing$rectification$task$view$new',
  '问题跟踪整改_食安稽核到店辅导整改任务(新)' = '$app$supervisor$home$food$safety$auditing$reach$shop$rectification$task$view$new',
  '问题跟踪整改_差异项到店巡检整改任务(新)' = '$app$supervisor$home$inspection$differenceItem$task$view$new',
}

/** 数据看板权限枚举 */
enum DataViewBoardPermissionEnum {
  自检实时情况 = '$app$supervisor$data$view$board$self$check$real$time$situation',
  自检实时情况_自检看板 = '$app$supervisor$data$view$board$self$check$real$time$situation$self$check$view$board',
  自检实时情况_到店巡检看板 = '$app$supervisor$data$view$board$self$check$real$time$situation$reach$shop$patrol$view$board',
  自检实时情况_视频云巡检看板 = '$app$supervisor$data$view$board$self$check$real$time$situation$video$cloud$patrol$view$board',
  自检实时情况_食安线下稽核看板 = '$app$supervisor$data$view$board$self$check$real$time$situation$food$safety$offline$auditing$view$board',
  自检实时情况_食安线上稽核看板 = '$app$supervisor$data$view$board$self$check$real$time$situation$food$safety$online$auditing$view$board',
  自检实时情况_诊断巡检看板 = '$app$supervisor$data$view$board$self$check$real$time$situation$diagnosis$patrol$view$board',
  // -----------------
  智慧运营看板 = '$app$supervisor$data$view$board$wisdom$operator$view$board',
  门店数据看板 = '$app$supervisor$data$view$board$store$data$view$board',
  // -----------------
  作战小组数据看板 = '$app$supervisor$data$view$board$fight$team$group$data$view$board',
  作战小组数据看板_业绩看板 = '$app$supervisor$data$view$board$fight$team$group$data$view$board$performance$view$board',
  作战小组数据看板_满意度数据看板 = '$app$supervisor$data$view$board$fight$team$group$data$view$board$degree$of$satisfaction$data$view$board',
  作战小组数据看板_食安数据看板 = '$app$supervisor$data$view$board$fight$team$group$data$view$board$food$safety$data$view$board',
  作战小组数据看板_人力数据看板 = '$app$supervisor$data$view$board$fight$team$group$data$view$board$manpower$data$view$board',
  作战小组数据看板_市场分析看板 = '$app$supervisor$data$view$board$fight$team$group$data$view$board$market$analysis$view$board',
}

export {
  HomePermissionEnum,
  SchedulePermissionEnum,
  MyPermissionEnum,
  DataViewBoardPermissionEnum,
  reportEntrancePermissions,
};
